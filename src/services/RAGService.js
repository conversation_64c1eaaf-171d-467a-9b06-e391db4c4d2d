// src/services/RAGService.js - Updated to copy all metadata chunks
import RNFS from 'react-native-fs';
import { Platform } from 'react-native';
import * as ort from 'onnxruntime-react-native';
import { NativeModules } from 'react-native';
import LocalTokenizer from '../utils/LocalTokenizer';
import BundledTokenizer from '../utils/BundledTokenizer';

const { HnswSearchModule } = NativeModules;

class RAGService {
  constructor() {
    this.embeddingSession = null;
    this.tokenizer = null;
    this.metadataIndex = null;
    this.metadataCache = new Map();
    this.cacheStats = { hits: 0, misses: 0 };
    this.isInitialized = false;
    this.isLoading = false;
  }

  // Initialize the RAG system
  async initialize() {
    if (this.isInitialized || this.isLoading) {
      return this.isInitialized;
    }

    this.isLoading = true;
    console.log("🚀 Initializing RAG Service...");

    try {
      await this._copyAssetsIfNeeded();
      await this._initializeEmbeddingModel();
      await this._initializeTokenizer();
      await this._loadMetadataIndex();

      this.isInitialized = true;
      console.log("✅ RAG Service initialized successfully");

      // Preload popular chunks in background
      setTimeout(() => this._preloadPopularChunks(), 2000);

      return true;
    } catch (error) {
      console.error("❌ RAG Service initialization failed:", error);
      this.isInitialized = false;
      throw error;
    } finally {
      this.isLoading = false;
    }
  }

  // Copy assets from bundle if needed (including all metadata chunks)
  async _copyAssetsIfNeeded() {
    const modelAssetPath = "all-MiniLM-L6-v2.quant.onnx";
    const modelPath = `${RNFS.DocumentDirectoryPath}/${modelAssetPath}`;
    const indexAssetPathData = "documents_hnsw_rs.hnsw.data";
    const indexPathData = `${RNFS.DocumentDirectoryPath}/${indexAssetPathData}`;
    const indexAssetPathGraph = "documents_hnsw_rs.hnsw.graph";
    const indexPathGraph = `${RNFS.DocumentDirectoryPath}/${indexAssetPathGraph}`;
    const metadataAssetPath = "metadata_index.json";
    const metadataPath = `${RNFS.DocumentDirectoryPath}/${metadataAssetPath}`;

    // Create metadata chunks directory
    const metadataChunksDir = `${RNFS.DocumentDirectoryPath}/metadata_chunks`;
    await RNFS.mkdir(metadataChunksDir);

    const copyAssetIfNeeded = async (assetPath, destPath) => {
      const exists = await RNFS.exists(destPath);
      if (!exists) {
        if (Platform.OS === 'ios') {
          const mainBundlePath = `${RNFS.MainBundlePath}/${assetPath}`;
          const fileExists = await RNFS.exists(mainBundlePath);
          if (fileExists) {
            await RNFS.copyFile(mainBundlePath, destPath);
            console.log(`📱 Copied from iOS bundle: ${assetPath}`);
          } else {
            throw new Error(`Asset not found: ${assetPath}`);
          }
        } else {
          await RNFS.copyFileAssets(assetPath, destPath);
          console.log(`🤖 Copied from Android assets: ${assetPath}`);
        }
      }
    };

    // Copy core files
    await copyAssetIfNeeded(modelAssetPath, modelPath);
    await copyAssetIfNeeded(indexAssetPathData, indexPathData);
    await copyAssetIfNeeded(indexAssetPathGraph, indexPathGraph);
    await copyAssetIfNeeded(metadataAssetPath, metadataPath);

    // Copy all metadata chunk files
    await this._copyMetadataChunks();
  }

  // Copy all metadata chunk files from assets
  async _copyMetadataChunks() {
    console.log("📦 Copying metadata chunks...");

    try {
      // First, load the metadata index to know how many chunks to copy
      const metadataIndexPath = `${RNFS.DocumentDirectoryPath}/metadata_index.json`;

      // Check if metadata index exists in assets and copy if needed
      let indexContent;
      try {
        indexContent = await RNFS.readFile(metadataIndexPath, 'utf8');
      } catch (error) {
        console.log("📄 Metadata index not found in documents directory, checking assets...");

        // Try to read from assets first
        let assetIndexPath;
        if (Platform.OS === 'ios') {
          assetIndexPath = `${RNFS.MainBundlePath}/metadata_index.json`;
        } else {
          // For Android, we need to copy it first
          await RNFS.copyFileAssets('metadata_index.json', metadataIndexPath);
          indexContent = await RNFS.readFile(metadataIndexPath, 'utf8');
        }

        if (Platform.OS === 'ios' && assetIndexPath) {
          indexContent = await RNFS.readFile(assetIndexPath, 'utf8');
        }
      }

      if (!indexContent) {
        throw new Error("Could not load metadata index");
      }

      const metadataIndex = JSON.parse(indexContent);
      const totalFiles = metadataIndex.total_files;

      console.log(`📦 Found ${totalFiles} metadata chunk files to copy`);

      // Copy each metadata chunk file
      const metadataChunksDir = `${RNFS.DocumentDirectoryPath}/metadata_chunks`;
      let copiedCount = 0;
      let skippedCount = 0;

      for (let i = 0; i < totalFiles; i++) {
        const chunkInfo = metadataIndex.files[i];
        const filename = chunkInfo.filename;
        const destPath = `${metadataChunksDir}/${filename}`;

        // Check if file already exists
        const exists = await RNFS.exists(destPath);
        if (!exists) {
          try {
            if (Platform.OS === 'ios') {
              const assetPath = `${RNFS.MainBundlePath}/${filename}`;
              const assetExists = await RNFS.exists(assetPath);
              const listedBundle = await RNFS.readDir(RNFS.MainBundlePath);
              const listedDocuments = await RNFS.readDir(RNFS.DocumentDirectoryPath);
              console.log("Bundle files:", listedBundle.map(f => f.name));
              console.log("Document files:", listedDocuments.map(f => f.name));
              if (assetExists) {
                await RNFS.copyFile(assetPath, destPath);
                copiedCount++;
              } else {
                console.warn(`⚠️ iOS asset not found: metadata_chunks/${filename}`);
              }
            } else {
              await RNFS.copyFileAssets(`metadata_chunks/${filename}`, destPath);
              copiedCount++;
            }
          } catch (error) {
            console.warn(`⚠️ Failed to copy ${filename}:`, error.message);
          }
        } else {
          skippedCount++;
        }
      }

      console.log(`✅ Metadata chunks copy complete: ${copiedCount} copied, ${skippedCount} already existed`);

      // Verify a few files were copied successfully
      if (copiedCount === 0 && skippedCount === 0) {
        throw new Error("No metadata chunk files were found or copied");
      }

    } catch (error) {
      console.error("❌ Failed to copy metadata chunks:", error);
      throw error;
    }
  }

  // Initialize ONNX embedding model
  async _initializeEmbeddingModel() {
    const modelPath = `${RNFS.DocumentDirectoryPath}/all-MiniLM-L6-v2.quant.onnx`;

    console.log("🧠 Loading ONNX embedding model...");
    const exists = await RNFS.exists(modelPath);
    if (!exists) {
      throw new Error(`Embedding model not found: ${modelPath}`);
    }

    try {
      this.embeddingSession = await ort.InferenceSession.create(modelPath);
      console.log("✅ ONNX embedding model loaded");
    } catch (error) {
      console.error("❌ Failed to load ONNX model:", error);
      throw error;
    }
  }

  // Initialize tokenizer
  async _initializeTokenizer() {
    console.log("🔤 Loading tokenizer...");

    try {
      const localTokenizer = new LocalTokenizer();
      const success = await localTokenizer.loadFromAssets();

      if (success) {
        this.tokenizer = localTokenizer;
        console.log("✅ Local tokenizer loaded");
        return;
      }
    } catch (error) {
      console.warn("⚠️ Local tokenizer failed, trying bundled tokenizer");
    }

    try {
      const bundledTokenizer = new BundledTokenizer();
      this.tokenizer = bundledTokenizer;
      console.log("✅ Bundled tokenizer loaded");
    } catch (error) {
      console.warn("⚠️ Bundled tokenizer failed, using fallback");
      this.tokenizer = null;
    }
  }

  // Load metadata index
  async _loadMetadataIndex() {
    console.log("📂 Loading metadata index...");

    const indexPath = `${RNFS.DocumentDirectoryPath}/metadata_index.json`;
    const exists = await RNFS.exists(indexPath);

    if (!exists) {
      throw new Error(`Metadata index not found: ${indexPath}`);
    }

    const indexContent = await RNFS.readFile(indexPath, 'utf8');
    this.metadataIndex = JSON.parse(indexContent);

    if (!this.metadataIndex.total_documents || !this.metadataIndex.files) {
      throw new Error("Invalid metadata index structure");
    }

    console.log("✅ Metadata index loaded:", {
      totalDocs: this.metadataIndex.total_documents,
      totalFiles: this.metadataIndex.total_files
    });
  }

  // Load metadata chunk with caching
  async _loadMetadataChunk(chunkIndex) {
    // Check cache first
    if (this.metadataCache.has(chunkIndex)) {
      this.cacheStats.hits++;
      return this.metadataCache.get(chunkIndex);
    }

    this.cacheStats.misses++;

    if (!this.metadataIndex || chunkIndex >= this.metadataIndex.total_files) {
      throw new Error(`Invalid chunk index ${chunkIndex}`);
    }

    const chunkInfo = this.metadataIndex.files[chunkIndex];
    const filename = chunkInfo.filename;
    const chunkPath = `${RNFS.DocumentDirectoryPath}/metadata_chunks/${filename}`;

    const exists = await RNFS.exists(chunkPath);
    if (!exists) {
      throw new Error(`Metadata chunk not found: ${chunkPath}`);
    }

    const chunkContent = await RNFS.readFile(chunkPath, 'utf8');
    const chunkData = JSON.parse(chunkContent);

    // LRU cache management
    const MAX_CACHE_SIZE = 8;
    if (this.metadataCache.size >= MAX_CACHE_SIZE) {
      const firstKey = this.metadataCache.keys().next().value;
      this.metadataCache.delete(firstKey);
    }

    this.metadataCache.set(chunkIndex, chunkData);
    return chunkData;
  }

  // Load document by index
  async loadDocument(docIndex) {
    if (!this.metadataIndex) {
      throw new Error("RAG service not initialized");
    }

    if (docIndex < 0 || docIndex >= this.metadataIndex.total_documents) {
      throw new Error(`Document index ${docIndex} out of range`);
    }

    const chunkIndex = Math.floor(docIndex / this.metadataIndex.docs_per_file);
    const positionInChunk = docIndex % this.metadataIndex.docs_per_file;

    const chunkData = await this._loadMetadataChunk(chunkIndex);

    if (positionInChunk >= chunkData.length) {
      throw new Error(`Position ${positionInChunk} out of range in chunk ${chunkIndex}`);
    }

    return chunkData[positionInChunk];
  }

  // Tokenize text
  async _tokenizeText(text, maxLength = 512) {
    if (this.tokenizer && this.tokenizer.isLoaded) {
      try {
        const encoded = await this.tokenizer.tokenize(text, maxLength);
        return {
          input_ids: encoded.input_ids,
          attention_mask: encoded.attention_mask
        };
      } catch (error) {
        console.warn("Tokenizer error, using fallback:", error);
      }
    }

    // Fallback tokenizer
    const cleanText = text.toLowerCase().trim();
    const processedText = cleanText
      .replace(/[^\w\s]/g, ' ')
      .replace(/\s+/g, ' ')
      .trim();

    const words = processedText.split(' ').filter(word => word.length > 0);
    const tokens = [101]; // [CLS] token

    for (const word of words.slice(0, maxLength - 2)) {
      let hash = 0;
      for (let i = 0; i < word.length; i++) {
        const char = word.charCodeAt(i);
        hash = ((hash << 5) - hash) + char;
        hash = hash & hash;
      }
      const tokenId = 1000 + Math.abs(hash % 28000);
      tokens.push(tokenId);
    }

    tokens.push(102); // [SEP] token

    while (tokens.length < maxLength) {
      tokens.push(0); // [PAD] token
    }

    const finalTokens = tokens.slice(0, maxLength);
    const attentionMask = finalTokens.map(id => id === 0 ? 0 : 1);

    return {
      input_ids: finalTokens,
      attention_mask: attentionMask
    };
  }

  // Generate embedding
  async embedText(text) {
    if (!this.embeddingSession) {
      throw new Error("Embedding session not initialized");
    }

    const tokenized = await this._tokenizeText(text, 512);

    const feeds = {
      'input_ids': new ort.Tensor('int64', tokenized.input_ids, [1, tokenized.input_ids.length]),
      'attention_mask': new ort.Tensor('int64', tokenized.attention_mask, [1, tokenized.attention_mask.length])
    };

    const results = await this.embeddingSession.run(feeds);

    // Try common output keys
    const possibleKeys = ['last_hidden_state', '1001', 'output', 'pooler_output'];
    let embedding = null;
    let usedKey = '';

    for (const key of possibleKeys) {
      if (results[key] && results[key].data && results[key].data.length > 0) {
        embedding = results[key].data;
        usedKey = key;
        break;
      }
    }

    if (!embedding) {
      const availableKeys = Object.keys(results).filter(key =>
        results[key] && results[key].data && results[key].data.length > 0);
      if (availableKeys.length > 0) {
        usedKey = availableKeys[0];
        embedding = results[usedKey].data;
      }
    }

    if (!embedding) {
      throw new Error("No suitable embedding output found");
    }

    // Apply mean pooling if 3D output
    let finalEmbedding = embedding;
    const outputShape = results[usedKey].dims;

    if (outputShape.length === 3) {
      const [batch, seqLen, hiddenSize] = outputShape;
      const pooled = new Float32Array(hiddenSize);

      for (let i = 0; i < hiddenSize; i++) {
        let sum = 0;
        let count = 0;
        for (let j = 0; j < seqLen; j++) {
          const idx = j * hiddenSize + i;
          if (tokenized.attention_mask[j] === 1) {
            sum += embedding[idx];
            count++;
          }
        }
        pooled[i] = count > 0 ? sum / count : 0;
      }
      finalEmbedding = pooled;
    }

    // Normalize
    const norm = Math.sqrt(Array.from(finalEmbedding).reduce((sum, val) => sum + val * val, 0));
    if (norm === 0) {
      throw new Error("Embedding norm is zero");
    }

    return new Float32Array(Array.from(finalEmbedding).map(val => val / norm));
  }

  // Query similar documents
  async querySimilarDocuments(query, k = 5) {
    if (!this.isInitialized) {
      throw new Error("RAG service not initialized");
    }

    const startTime = Date.now();

    // Generate embedding
    const embedding = await this.embedText(query);

    // Search HNSW index
    if (!HnswSearchModule) {
      throw new Error("HNSW module not available");
    }

    const results = await HnswSearchModule.searchKnn(
      Array.from(embedding),
      embedding.length,
      k,
      RNFS.DocumentDirectoryPath,
      "documents_hnsw_rs"
    );

    // Load documents
    const retrievedDocs = [];
    for (let i = 0; i < results.neighbors.length; i++) {
      const idx = results.neighbors[i];
      const distance = results.distances ? results.distances[i] : 'unknown';

      try {
        const doc = await this.loadDocument(idx);
        if (doc) {
          retrievedDocs.push({
            ...doc,
            distance,
            retrievalIndex: i,
            relevanceScore: 1 / (1 + parseFloat(distance))
          });
        }
      } catch (error) {
        console.warn(`Failed to load document ${idx}:`, error);
      }
    }

    const queryTime = Date.now() - startTime;

    console.log(`🎯 RAG query completed in ${queryTime}ms: ${retrievedDocs.length}/${k} docs`);

    return {
      documents: retrievedDocs,
      queryTime,
      cacheStats: { ...this.cacheStats }
    };
  }

  // Preload popular chunks
  async _preloadPopularChunks() {
    if (!this.metadataIndex) return;

    const chunksToPreload = Math.min(3, this.metadataIndex.total_files);
    console.log("🚀 Preloading first few chunks...");

    for (let i = 0; i < chunksToPreload; i++) {
      try {
        await this._loadMetadataChunk(i);
        console.log(`✅ Preloaded chunk ${i}`);
      } catch (error) {
        console.warn(`Failed to preload chunk ${i}:`, error);
      }
    }
  }

  // Get statistics
  getStats() {
    return {
      isInitialized: this.isInitialized,
      totalDocuments: this.metadataIndex?.total_documents || 0,
      totalChunks: this.metadataIndex?.total_files || 0,
      cachedChunks: this.metadataCache.size,
      cacheHitRate: this.cacheStats.hits + this.cacheStats.misses > 0
        ? Math.round(this.cacheStats.hits / (this.cacheStats.hits + this.cacheStats.misses) * 100)
        : 0,
      memoryEstimate: `${Math.round(this.metadataCache.size * 5)}MB`,
    };
  }

  // Clear caches
  clearCaches() {
    this.metadataCache.clear();
    this.cacheStats = { hits: 0, misses: 0 };
    console.log("🧹 RAG caches cleared");
  }

  // Test specific term embedding
  async testTermEmbedding(term) {
    if (!this.isInitialized) {
      return { error: "RAG service not initialized" };
    }

    try {
      console.log(`🔍 Testing embedding for term: "${term}"`);

      // Test tokenization
      let tokenizationResult = null;
      if (this.tokenizer) {
        try {
          tokenizationResult = await this.tokenizer.tokenize(term);
          console.log(`🔤 Tokenization result:`, tokenizationResult);
        } catch (error) {
          console.warn(`⚠️ Tokenization failed:`, error);
        }
      }

      // Test embedding generation
      const embedding = await this.embedText(term);
      console.log(`🧮 Embedding generated: ${embedding.length} dimensions`);
      console.log(`🧮 First 5 values: [${Array.from(embedding.slice(0, 5)).map(v => v.toFixed(4)).join(', ')}]`);

      // Test HNSW search
      const searchResults = await this.querySimilarDocuments(term, 5);
      console.log(`🎯 Search results: ${searchResults.documents.length} documents found`);

      return {
        term,
        platform: Platform.OS,
        tokenizerType: this.tokenizer?.constructor?.name || 'unknown',
        tokenization: tokenizationResult,
        embeddingDimensions: embedding.length,
        embeddingPreview: Array.from(embedding.slice(0, 5)).map(v => v.toFixed(4)),
        searchResultCount: searchResults.documents.length,
        searchResults: searchResults.documents.map(doc => ({
          id: doc.id,
          distance: doc.distance,
          relevanceScore: doc.relevanceScore,
          contentPreview: doc.content?.substring(0, 100) + '...'
        }))
      };
    } catch (error) {
      console.error(`❌ Term embedding test failed:`, error);
      return { error: error.message };
    }
  }

  // Run diagnostics
  async runDiagnostics() {
    const diagnostics = {
      timestamp: new Date().toISOString(),
      platform: Platform.OS,
      results: {}
    };

    try {
      // Test metadata index
      if (this.metadataIndex) {
        diagnostics.results.metadataIndex = "PASSED";
      } else {
        diagnostics.results.metadataIndex = "FAILED - Not loaded";
      }

      // Test HNSW files
      const graphExists = await RNFS.exists(`${RNFS.DocumentDirectoryPath}/documents_hnsw_rs.hnsw.graph`);
      const dataExists = await RNFS.exists(`${RNFS.DocumentDirectoryPath}/documents_hnsw_rs.hnsw.data`);

      diagnostics.results.hnswFiles = graphExists && dataExists ? "PASSED" : "FAILED";

      // Test metadata chunks directory
      const metadataChunksDir = `${RNFS.DocumentDirectoryPath}/metadata_chunks`;
      const chunksExist = await RNFS.exists(metadataChunksDir);
      if (chunksExist) {
        const chunkFiles = await RNFS.readDir(metadataChunksDir);
        diagnostics.results.metadataChunks = `PASSED - ${chunkFiles.length} files`;
      } else {
        diagnostics.results.metadataChunks = "FAILED - Directory not found";
      }

      // Test random document loading
      if (this.metadataIndex) {
        const randomIndex = Math.floor(Math.random() * Math.min(100, this.metadataIndex.total_documents));
        try {
          const doc = await this.loadDocument(randomIndex);
          diagnostics.results.documentLoading = doc ? "PASSED" : "FAILED";
        } catch (error) {
          diagnostics.results.documentLoading = `FAILED - ${error.message}`;
        }
      }

      // Test embedding
      if (this.embeddingSession) {
        try {
          const embedding = await this.embedText("test");
          diagnostics.results.embeddingModel = embedding ? "PASSED" : "FAILED";
        } catch (error) {
          diagnostics.results.embeddingModel = "FAILED";
        }
      }

      // Test HNSW module
      diagnostics.results.hnswModule = HnswSearchModule ? "PASSED" : "FAILED";

      // Test tokenizer type
      diagnostics.results.tokenizerType = this.tokenizer?.constructor?.name || 'unknown';

    } catch (error) {
      diagnostics.results.error = error.message;
    }

    console.log("📊 RAG Diagnostics:", diagnostics);
    return diagnostics;
  }
}

// Export singleton instance
export default new RAGService();