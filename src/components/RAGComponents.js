// src/components/RAGComponents.js
import React, { useState } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  Switch,
  StyleSheet,
  Platform,
} from 'react-native';
import Markdown from 'react-native-markdown-display';
import { getOptimalChunkCount } from '../config/ModelConfig';

// Enhanced message bubble with simplified RAG context
export const EnhancedMessageBubble = ({
  message,
  index,
  tokensPerSecond,
  onToggleContext,
  onToggleThought,
  userBubbleStyle,
  llamaBubbleStyle
}) => {
  return (
    <View style={styles.messageWrapper}>
      <View
        style={[
          styles.messageBubble,
          message.role === "user" ? userBubbleStyle : llamaBubbleStyle,
        ]}
      >
        {/* RAG Context Toggle */}
        {message.context && (
          <TouchableOpacity
            onPress={() => onToggleContext(index)}
            style={styles.toggleButton}
          >
            <Text style={styles.ragToggleText}>
              {message.showContext
                ? "▼ Hide Retrieved Context"
                : `▶ Show Retrieved Context (${message.retrievedDocs?.length || 0} docs)`}
            </Text>
          </TouchableOpacity>
        )}

        {/* Simplified Context Display */}
        {message.showContext && message.context && (
          <View style={styles.contextContainer}>
            <Text style={styles.contextTitle}>
              📚 Retrieved Knowledge ({message.retrievedDocs?.length || 0} documents):
            </Text>

            {/* Show individual document sources */}
            {message.retrievedDocs?.map((doc, idx) => (
              <View key={idx} style={styles.contextDocItem}>
                <Text style={styles.contextDocHeader}>
                  📄 Doc {idx + 1}: {doc.metadata?.source || 'Unknown'}
                  {doc.distance && ` (${(1/(1+parseFloat(doc.distance))*100).toFixed(1)}% match)`}
                </Text>
                <Text style={styles.contextDocPreview}>
                  {doc.content.substring(0, 200)}...
                </Text>
              </View>
            )) || (
              <Text style={styles.contextText}>{message.context}</Text>
            )}
          </View>
        )}

        {/* Thought Toggle */}
        {message.thought && (
          <TouchableOpacity
            onPress={() => onToggleThought(index)}
            style={styles.toggleButton}
          >
            <Text style={styles.toggleText}>
              {message.showThought ? "▼ Hide Thought" : "▶ Show Thought"}
            </Text>
          </TouchableOpacity>
        )}

        {message.showThought && message.thought && (
          <View style={styles.thoughtContainer}>
            <Text style={styles.thoughtTitle}>🤔 Model's Reasoning:</Text>
            <Text style={styles.thoughtText}>{message.thought}</Text>
          </View>
        )}

        {/* Main message content */}
        <Markdown
          style={{
            body: {
              color: message.role === "user" ? "#FFFFFF" : "#334155",
              fontSize: 16,
            },
            paragraph: {
              color: message.role === "user" ? "#FFFFFF" : "#334155",
              fontSize: 16,
              marginTop: 0,
              marginBottom: 8,
            },
            text: {
              color: message.role === "user" ? "#FFFFFF" : "#334155",
              fontSize: 16,
            }
          }}
        >
          {message.content}
        </Markdown>
      </View>

      {/* Fixed metrics display */}
      {message.role === "assistant" && (
        <View style={styles.messageStatsContainer}>
          {(() => {
            // Calculate the correct index for performance data
            // Since conversation.slice(1) removes system message and index is passed as index + 1,
            // we need to adjust: assistant messages are at odd indices (1, 3, 5...)
            // Performance data index should be: (index - 1) / 2
            const performanceIndex = Math.floor((index - 1) / 2);
            const performanceData = tokensPerSecond[performanceIndex];

            if (!performanceData) {
              return (
                <Text style={styles.tokenInfo}>
                  Processing...
                </Text>
              );
            }

            // Handle both old format (number) and new format (object)
            if (typeof performanceData === 'number') {
              return (
                <Text style={styles.tokenInfo}>
                  {performanceData} tokens/s
                  {message.queryTime && ` • RAG: ${message.queryTime}ms`}
                </Text>
              );
            }

            // New object format
            const {
              tokensPerSecond: tps,
              ttft,
              ragTime,
              totalTokens
            } = performanceData;

            return (
              <Text style={styles.tokenInfo}>
                {tps} tokens/s • TTFT: {ttft}s
                {ragTime > 0 && ` • RAG: ${ragTime}s`}
                {totalTokens && ` • ${totalTokens} tokens`}
              </Text>
            );
          })()}
        </View>
      )}
    </View>
  );
};

// Simplified RAG status bar
export const RAGStatusBar = ({
  ragEnabled,
  onToggleRAG,
  ragStats,
  docCount
}) => {
  if (!ragStats?.isInitialized) return null;

  return (
    <View style={styles.ragStatusRow}>
      <View style={styles.ragStatusLeft}>
        <Text style={styles.ragLabel}>RAG</Text>
        <Switch
          value={ragEnabled}
          onValueChange={onToggleRAG}
          trackColor={{ false: "#767577", true: "#3B82F6" }}
          thumbColor={ragEnabled ? "#FFFFFF" : "#f4f3f4"}
        />
      </View>

      <View style={styles.ragStatusRight}>
        <Text style={styles.ragStatusText}>
          {ragStats.totalDocuments.toLocaleString()} docs available
          {docCount && ` • ${docCount} per query`}
        </Text>
      </View>
    </View>
  );
};

// Enhanced debug panel with collapsible functionality
export const DebugPanel = ({
  ragStats,
  onRunDiagnostics,
  onClearCaches,
  docCount,
  onDocCountChange,
  selectedModel
}) => {
  const [isExpanded, setIsExpanded] = useState(false);

  // Always show the header, but only show content when expanded
  return (
    <View style={styles.debugContainer}>
      <TouchableOpacity
        onPress={() => setIsExpanded(!isExpanded)}
        style={styles.debugHeader}
      >
        <Text style={styles.debugTitle}>
          {isExpanded ? '🔽' : '▶️'} Debug Panel {__DEV__ ? '(Dev)' : ''}
        </Text>
      </TouchableOpacity>

      {isExpanded && (
        <View style={styles.debugContent}>
          {/* RAG Statistics */}
          <Text style={styles.debugText}>
            Platform: {Platform.OS}{'\n'}
            RAG Initialized: {ragStats?.isInitialized ? '✅' : '❌'}{'\n'}
            Total Documents: {ragStats?.totalDocuments?.toLocaleString() || 0}{'\n'}
            Cached Chunks: {ragStats?.cachedChunks || 0}{'\n'}
            Cache Hit Rate: {ragStats?.cacheHitRate || 0}%{'\n'}
            Memory Usage: {ragStats?.memoryEstimate || 'N/A'}{'\n'}
            Current Doc Count: {docCount} {docCount !== getOptimalChunkCount(selectedModel) ? '(Custom)' : '(Auto)'}
          </Text>

          {/* Document Count Selector */}
          <View style={styles.docCountContainer}>
            <View style={styles.docCountHeader}>
              <Text style={styles.docCountLabel}>Documents per query:</Text>
              <TouchableOpacity
                onPress={() => onDocCountChange(null)}
                style={styles.resetButton}
              >
                <Text style={styles.resetButtonText}>Auto</Text>
              </TouchableOpacity>
            </View>
            <View style={styles.docCountSelector}>
              {[3, 5, 8, 10, 15, 20, 25].map((count) => (
                <TouchableOpacity
                  key={count}
                  style={[
                    styles.docCountButton,
                    docCount === count && styles.docCountButtonSelected
                  ]}
                  onPress={() => onDocCountChange(count)}
                >
                  <Text style={[
                    styles.docCountButtonText,
                    docCount === count && styles.docCountButtonTextSelected
                  ]}>
                    {count}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>
          </View>

          {/* Action Buttons */}
          <View style={styles.debugActions}>
            <TouchableOpacity
              onPress={onRunDiagnostics}
              style={styles.debugButton}
            >
              <Text style={styles.debugButtonText}>Run Diagnostics</Text>
            </TouchableOpacity>

            <TouchableOpacity
              onPress={onClearCaches}
              style={[styles.debugButton, styles.clearCacheButton]}
            >
              <Text style={styles.debugButtonText}>Clear Cache</Text>
            </TouchableOpacity>
          </View>
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  messageWrapper: {
    marginBottom: 16,
  },
  messageBubble: {
    padding: 12,
    borderRadius: 12,
    maxWidth: "80%",
  },
  messageStatsContainer: {
    marginTop: 4,
  },
  tokenInfo: {
    fontSize: 12,
    color: "#94A3B8",
    textAlign: "right",
  },
  toggleButton: {
    marginBottom: 8,
    paddingVertical: 4,
  },
  ragToggleText: {
    color: "#3B82F6",
    fontSize: 12,
    fontWeight: "500",
  },
  toggleText: {
    color: "#3B82F6",
    fontSize: 12,
    fontWeight: "500",
  },
  contextContainer: {
    marginBottom: 12,
    padding: 10,
    backgroundColor: "#EFF6FF",
    borderRadius: 8,
    borderLeftWidth: 3,
    borderLeftColor: "#3B82F6",
  },
  contextTitle: {
    color: "#3B82F6",
    fontSize: 12,
    fontWeight: "600",
    marginBottom: 8,
  },
  contextText: {
    color: "#334155",
    fontSize: 12,
    lineHeight: 16,
  },
  contextDocItem: {
    marginBottom: 8,
    paddingBottom: 8,
    borderBottomWidth: 1,
    borderBottomColor: "#E2E8F0",
  },
  contextDocHeader: {
    color: "#1E40AF",
    fontSize: 11,
    fontWeight: "600",
    marginBottom: 4,
  },
  contextDocPreview: {
    color: "#475569",
    fontSize: 11,
    lineHeight: 14,
    fontStyle: "italic",
  },
  thoughtContainer: {
    marginBottom: 12,
    padding: 10,
    backgroundColor: "#F1F5F9",
    borderRadius: 8,
    borderLeftWidth: 3,
    borderLeftColor: "#94A3B8",
  },
  thoughtTitle: {
    color: "#64748B",
    fontSize: 12,
    fontWeight: "600",
    marginBottom: 4,
  },
  thoughtText: {
    color: "#475569",
    fontSize: 12,
    fontStyle: "italic",
    lineHeight: 16,
  },
  ragStatusRow: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    paddingHorizontal: 16,
    paddingVertical: 8,
    backgroundColor: "#F8FAFC",
    borderBottomWidth: 1,
    borderBottomColor: "#E2E8F0",
  },
  ragStatusLeft: {
    flexDirection: "row",
    alignItems: "center",
  },
  ragStatusRight: {
    flexDirection: "row",
    alignItems: "center",
  },
  ragLabel: {
    marginRight: 8,
    fontSize: 14,
    fontWeight: "600",
    color: "#334155",
  },
  ragStatusText: {
    fontSize: 12,
    color: "#64748B",
  },
  debugContainer: {
    backgroundColor: "#FEF3C7",
    borderRadius: 12,
    padding: 12,
    marginHorizontal: 16,
    marginBottom: 8,
    borderWidth: 1,
    borderColor: "#FCD34D",
  },
  debugHeader: {
    paddingVertical: 4,
  },
  debugTitle: {
    fontSize: 12,
    fontWeight: "600",
    color: "#92400E",
  },
  debugContent: {
    marginTop: 8,
  },
  debugText: {
    fontSize: 10,
    color: "#92400E",
    fontFamily: Platform.OS === 'ios' ? 'Menlo' : 'monospace',
    lineHeight: 12,
    marginBottom: 12,
  },
  docCountContainer: {
    marginBottom: 12,
  },
  docCountHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 6,
  },
  docCountLabel: {
    fontSize: 11,
    fontWeight: "600",
    color: "#92400E",
  },
  resetButton: {
    backgroundColor: "#10B981",
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 4,
  },
  resetButtonText: {
    fontSize: 9,
    fontWeight: "600",
    color: "#FFFFFF",
  },
  docCountSelector: {
    flexDirection: "row",
    flexWrap: "wrap",
    gap: 4,
  },
  docCountButton: {
    backgroundColor: "#FFFFFF",
    borderWidth: 1,
    borderColor: "#D97706",
    borderRadius: 4,
    paddingHorizontal: 8,
    paddingVertical: 4,
    minWidth: 32,
    alignItems: "center",
  },
  docCountButtonSelected: {
    backgroundColor: "#D97706",
  },
  docCountButtonText: {
    fontSize: 10,
    fontWeight: "600",
    color: "#D97706",
  },
  docCountButtonTextSelected: {
    color: "#FFFFFF",
  },
  debugActions: {
    flexDirection: "row",
    gap: 8,
  },
  debugButton: {
    backgroundColor: "#8B5CF6",
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 6,
    flex: 1,
    alignItems: "center",
  },
  clearCacheButton: {
    backgroundColor: "#EF4444",
  },
  debugButtonText: {
    color: "#FFFFFF",
    fontSize: 10,
    fontWeight: "600",
  },
});