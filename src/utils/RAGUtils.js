// src/utils/RAGUtils.js
import RAGService from '../services/RAGService';

// Format retrieved documents into context string
export const formatRetrievedContext = (retrievedDocs) => {
  if (!retrievedDocs || retrievedDocs.length === 0) {
    return "";
  }

  return retrievedDocs.map(doc => {
    const chunkInfo = doc.metadata?.chunk_index !== undefined
      ? ` (chunk ${doc.metadata.chunk_index + 1}/${doc.metadata.total_chunks})`
      : '';

    return `[Source: ${doc.metadata?.source || 'Unknown'}${chunkInfo}]\n${doc.content}`;
  }).join('\n\n---\n\n');
};

// Enhanced system message creation with RAG context
export const createEnhancedSystemMessage = (baseSystemMessage, retrievedContext) => {
  if (!retrievedContext) {
    return baseSystemMessage;
  }

  return {
    role: "system",
    content: `${baseSystemMessage}

Use the following retrieved information to help answer the user's question when relevant. Cite sources when possible.

Retrieved Information:
${retrievedContext}

Guidelines:
- Use the retrieved information when it's relevant to the user's question
- If information conflicts with your knowledge, mention both perspectives
- Cite sources using the format [Source: filename]
- If retrieved information isn't helpful, rely on your general knowledge
- Be concise and focus on the most relevant information`
  };
};

// Smart prefetching based on conversation context
export const smartPrefetch = async (conversationHistory) => {
  if (!conversationHistory || conversationHistory.length < 2) {
    return;
  }

  console.log("🧠 Analyzing conversation for smart prefetching...");

  // Extract key terms from recent messages
  const recentMessages = conversationHistory.slice(-3);
  const keyTerms = new Set();
  const commonWords = new Set([
    'what', 'how', 'when', 'where', 'why', 'that', 'this', 'with',
    'from', 'they', 'have', 'been', 'will', 'would', 'could', 'should',
    'the', 'and', 'for', 'are', 'but', 'not', 'you', 'all', 'can'
  ]);

  recentMessages.forEach(msg => {
    if (msg.role === 'user') {
      const words = msg.content.toLowerCase()
        .replace(/[^\w\s]/g, ' ')
        .split(/\s+/)
        .filter(word =>
          word.length > 3 &&
          !commonWords.has(word) &&
          !word.match(/^\d+$/) // Exclude pure numbers
        );

      words.forEach(word => keyTerms.add(word));
    }
  });

  if (keyTerms.size === 0) return;

  console.log("🔍 Key terms for prefetching:", Array.from(keyTerms).slice(0, 5));

  // This could be enhanced to:
  // 1. Use embeddings to find semantically similar content
  // 2. Track which document chunks were most useful historically
  // 3. Use ML to predict what documents might be needed next

  // For now, just a simple approach - you could expand this
  return Array.from(keyTerms);
};

// Batch document loading for efficiency
export const batchLoadDocuments = async (documentIndices) => {
  if (!documentIndices || documentIndices.length === 0) {
    return [];
  }

  console.log(`📦 Batch loading ${documentIndices.length} documents...`);

  const results = [];
  const errors = [];

  // Load documents concurrently (but limit concurrency to avoid overwhelming the system)
  const BATCH_SIZE = 5;

  for (let i = 0; i < documentIndices.length; i += BATCH_SIZE) {
    const batch = documentIndices.slice(i, i + BATCH_SIZE);

    const batchPromises = batch.map(async (docIndex, batchIndex) => {
      try {
        const doc = await RAGService.loadDocument(docIndex);
        return { index: i + batchIndex, doc };
      } catch (error) {
        console.warn(`Failed to load document ${docIndex}:`, error);
        errors.push({ index: i + batchIndex, docIndex, error: error.message });
        return { index: i + batchIndex, doc: null };
      }
    });

    const batchResults = await Promise.all(batchPromises);
    batchResults.forEach(result => {
      results[result.index] = result.doc;
    });
  }

  const loadedCount = results.filter(doc => doc != null).length;
  console.log(`📦 Batch loaded ${loadedCount}/${documentIndices.length} documents`);

  if (errors.length > 0) {
    console.warn(`⚠️ ${errors.length} documents failed to load:`, errors);
  }

  return results;
};

// Analyze query complexity to determine optimal RAG parameters
export const analyzeQueryComplexity = (query) => {
  const words = query.trim().split(/\s+/);
  const sentences = query.split(/[.!?]+/).filter(s => s.trim().length > 0);
  const questionWords = ['what', 'how', 'why', 'when', 'where', 'who', 'which'];
  const hasQuestionWords = questionWords.some(qw =>
    query.toLowerCase().includes(qw)
  );

  // Complexity scoring
  let complexity = 1.0;

  // Length factors
  if (words.length > 15) complexity += 0.3;
  if (words.length > 25) complexity += 0.3;
  if (sentences.length > 2) complexity += 0.2;

  // Question complexity
  if (hasQuestionWords) complexity += 0.2;
  if (query.includes('compare') || query.includes('contrast')) complexity += 0.4;
  if (query.includes('analyze') || query.includes('explain')) complexity += 0.3;
  if (query.includes('list') || query.includes('summarize')) complexity += 0.2;

  // Technical terms (you could expand this based on your domain)
  const technicalTerms = ['algorithm', 'implementation', 'architecture', 'framework'];
  if (technicalTerms.some(term => query.toLowerCase().includes(term))) {
    complexity += 0.2;
  }

  return {
    score: Math.min(complexity, 2.0), // Cap at 2.0
    wordCount: words.length,
    sentenceCount: sentences.length,
    hasQuestionWords,
    category: complexity < 1.2 ? 'simple' :
              complexity < 1.6 ? 'moderate' : 'complex'
  };
};

// Get adaptive chunk count based on query complexity
export const getAdaptiveChunkCount = (query, baseChunkCount) => {
  const complexity = analyzeQueryComplexity(query);

  let multiplier = 1.0;

  switch (complexity.category) {
    case 'simple':
      multiplier = 0.8; // Use fewer chunks for simple queries
      break;
    case 'moderate':
      multiplier = 1.0; // Use base count
      break;
    case 'complex':
      multiplier = 1.4; // Use more chunks for complex queries
      break;
  }

  const adaptedCount = Math.round(baseChunkCount * multiplier);
  const finalCount = Math.max(3, Math.min(adaptedCount, 25)); // Keep within reasonable bounds

  console.log(`🎯 Query complexity: ${complexity.category} (${complexity.score.toFixed(2)}) -> ${finalCount} chunks`);

  return finalCount;
};

// Calculate relevance score between query and document
export const calculateRelevanceScore = (query, document) => {
  if (!query || !document || !document.content) {
    return 0;
  }

  const queryTerms = query.toLowerCase()
    .replace(/[^\w\s]/g, ' ')
    .split(/\s+/)
    .filter(term => term.length > 2);

  const docContent = document.content.toLowerCase();
  const docWords = docContent.split(/\s+/);

  let matches = 0;
  let totalTerms = queryTerms.length;

  queryTerms.forEach(term => {
    if (docContent.includes(term)) {
      matches++;
      // Give extra weight to exact word matches vs substring matches
      const exactMatches = docWords.filter(word => word === term).length;
      if (exactMatches > 0) {
        matches += 0.5; // Bonus for exact word matches
      }
    }
  });

  const baseScore = totalTerms > 0 ? matches / totalTerms : 0;

  // Boost score based on document metadata
  let metadataBoost = 0;
  if (document.metadata) {
    // Boost if query terms appear in source filename
    const sourceBoost = queryTerms.some(term =>
      document.metadata.source?.toLowerCase().includes(term)
    ) ? 0.2 : 0;

    // Boost based on document type (you can customize this)
    const typeBoost = document.metadata.type === 'pdf' ? 0.1 : 0;

    metadataBoost = sourceBoost + typeBoost;
  }

  return Math.min(baseScore + metadataBoost, 1.0);
};

// Filter and rank retrieved documents by relevance
export const rankDocumentsByRelevance = (query, documents, maxResults = null) => {
  if (!documents || documents.length === 0) {
    return [];
  }

  // Calculate relevance scores
  const scoredDocs = documents.map(doc => ({
    ...doc,
    relevanceScore: calculateRelevanceScore(query, doc),
    // Combine with distance-based score if available
    combinedScore: doc.distance ?
      (calculateRelevanceScore(query, doc) + (1 / (1 + parseFloat(doc.distance)))) / 2 :
      calculateRelevanceScore(query, doc)
  }));

  // Sort by combined score (descending)
  const rankedDocs = scoredDocs.sort((a, b) => b.combinedScore - a.combinedScore);

  // Filter out documents with very low relevance
  const relevantDocs = rankedDocs.filter(doc => doc.relevanceScore > 0.1);

  // Return top results if maxResults is specified
  return maxResults ? relevantDocs.slice(0, maxResults) : relevantDocs;
};

// Memory management utilities
export const getMemoryEstimate = () => {
  const ragStats = RAGService.getStats();
  const chunkSizeEstimate = 5; // MB per chunk
  const baseMemory = 10; // Base memory for service

  return {
    ragService: baseMemory,
    cachedChunks: ragStats.cachedChunks * chunkSizeEstimate,
    total: baseMemory + (ragStats.cachedChunks * chunkSizeEstimate),
    recommendation: ragStats.cachedChunks > 6 ? 'Consider clearing cache' : 'Memory usage optimal'
  };
};

// Performance monitoring
export const trackRAGPerformance = (operation, startTime, metadata = {}) => {
  const duration = Date.now() - startTime;
  const stats = RAGService.getStats();

  const performanceData = {
    operation,
    duration,
    timestamp: new Date().toISOString(),
    cacheHitRate: stats.cacheHitRate,
    cachedChunks: stats.cachedChunks,
    ...metadata
  };

  console.log(`⏱️ RAG Performance [${operation}]: ${duration}ms`, performanceData);

  // You could send this to analytics service
  return performanceData;
};

// Export all utilities
export default {
  formatRetrievedContext,
  createEnhancedSystemMessage,
  smartPrefetch,
  batchLoadDocuments,
  analyzeQueryComplexity,
  getAdaptiveChunkCount,
  calculateRelevanceScore,
  rankDocumentsByRelevance,
  getMemoryEstimate,
  trackRAGPerformance
};