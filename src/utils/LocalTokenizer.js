/**
 * Local tokenizer implementation that uses the same vocabulary as the Python script
 * This ensures exact consistency between Python and React Native tokenization
 */

import RNFS from 'react-native-fs';
import { Platform } from 'react-native';

class LocalTokenizer {
  constructor() {
    this.vocab = null;
    this.config = null;
    this.vocabToId = null;
    this.idToVocab = null;
    this.isLoaded = false;
  }

  async loadFromAssets() {
    try {
      console.log('🔤 Loading local tokenizer from assets...');

      // Use the same pattern as your existing file loading (copyAssetIfNeeded)
      const copyAssetIfNeeded = async (assetPath, destPath) => {
        const exists = await RNFS.exists(destPath);
        if (!exists) {
          if (Platform.OS === 'ios') {
            // For iOS, use the main bundle path
            const mainBundlePath = `${RNFS.MainBundlePath}/${assetPath}`;
            const fileExists = await RNFS.exists(mainBundlePath);

            if (fileExists) {
              await RNFS.copyFile(mainBundlePath, destPath);
              console.log(`Copied from bundle: ${mainBundlePath} to ${destPath}`);
            } else {
              console.error(`File not found in main bundle: ${mainBundlePath}`);
              throw new Error(`Asset not found: ${assetPath}`);
            }
          } else {
            // For Android, use copyFileAssets
            await RNFS.copyFileAssets(assetPath, destPath);
            console.log(`Copied asset: ${assetPath} to ${destPath}`);
          }
        } else {
          console.log(`File already exists at: ${destPath}`);
        }
      };

      // Define paths (same pattern as your ONNX/HNSW files)
      const vocabAssetPath = "vocab.json";
      const vocabPath = `${RNFS.DocumentDirectoryPath}/vocab.json`;
      const configAssetPath = "tokenizer_config_rn.json";
      const configPath = `${RNFS.DocumentDirectoryPath}/tokenizer_config_rn.json`;
      const testAssetPath = "test_example.json";
      const testPath = `${RNFS.DocumentDirectoryPath}/test_example.json`;

      // Copy the tokenizer files (same as your other assets)
      try {
        await copyAssetIfNeeded(vocabAssetPath, vocabPath);
        await copyAssetIfNeeded(configAssetPath, configPath);
        await copyAssetIfNeeded(testAssetPath, testPath);
      } catch (error) {
        console.error("Error copying tokenizer assets:", error);
        throw new Error("Failed to copy tokenizer asset files");
      }

      // Load vocabulary
      console.log('📖 Loading vocabulary from:', vocabPath);
      const vocabContent = await RNFS.readFile(vocabPath, 'utf8');
      this.vocab = JSON.parse(vocabContent);

      // Create reverse mapping
      this.vocabToId = this.vocab;
      this.idToVocab = {};
      for (const [token, id] of Object.entries(this.vocab)) {
        this.idToVocab[id] = token;
      }
      console.log('✅ Vocabulary loaded:', Object.keys(this.vocab).length, 'tokens');

      // Load config
      console.log('⚙️ Loading config from:', configPath);
      const configContent = await RNFS.readFile(configPath, 'utf8');
      this.config = JSON.parse(configContent);
      console.log('✅ Config loaded');

      // Load and verify test example
      console.log('🧪 Loading test example from:', testPath);
      const testContent = await RNFS.readFile(testPath, 'utf8');
      const testExample = JSON.parse(testContent);
      console.log('✅ Test example loaded');

      // Mark as loaded before verification (needed for tokenize method)
      this.isLoaded = true;

      // Verify our tokenizer works correctly
      const testResult = await this.tokenize(testExample.text, 512);
      const matches = JSON.stringify(testResult.input_ids.slice(0, 10)) ===
                     JSON.stringify(testExample.input_ids.slice(0, 10));

      if (matches) {
        console.log('✅ Tokenizer verification successful - matches Python output!');
      } else {
        console.warn('⚠️ Tokenizer verification failed - output differs from Python');
        console.log('Expected:', testExample.input_ids.slice(0, 10));
        console.log('Got:', testResult.input_ids.slice(0, 10));
      }
      console.log(`✅ Local tokenizer loaded successfully`);
      console.log(`📊 Vocabulary size: ${Object.keys(this.vocab).length}`);
      console.log(`🔧 Special tokens:`, this.config.special_tokens);
      
      return true;
    } catch (error) {
      console.error('❌ Failed to load local tokenizer:', error);
      return false;
    }
  }

  // Basic BERT-style tokenization
  basicTokenize(text) {
    // Convert to lowercase and handle basic punctuation
    text = text.toLowerCase().trim();
    
    // Split on whitespace and punctuation
    const tokens = [];
    let currentToken = '';
    
    for (let i = 0; i < text.length; i++) {
      const char = text[i];
      
      if (/\s/.test(char)) {
        // Whitespace
        if (currentToken) {
          tokens.push(currentToken);
          currentToken = '';
        }
      } else if (/[^\w]/.test(char)) {
        // Punctuation
        if (currentToken) {
          tokens.push(currentToken);
          currentToken = '';
        }
        tokens.push(char);
      } else {
        // Regular character
        currentToken += char;
      }
    }
    
    if (currentToken) {
      tokens.push(currentToken);
    }
    
    return tokens;
  }

  // WordPiece tokenization (simplified)
  wordPieceTokenize(token) {
    if (this.vocabToId[token]) {
      return [token];
    }
    
    // Try to find subword tokens
    const subTokens = [];
    let start = 0;
    
    while (start < token.length) {
      let end = token.length;
      let foundSubToken = null;
      
      // Try to find the longest subword token
      while (start < end) {
        const subToken = start === 0 ? token.substring(start, end) : '##' + token.substring(start, end);
        
        if (this.vocabToId[subToken]) {
          foundSubToken = subToken;
          break;
        }
        end--;
      }
      
      if (foundSubToken) {
        subTokens.push(foundSubToken);
        start = end;
      } else {
        // Use [UNK] token
        subTokens.push('[UNK]');
        start++;
      }
    }
    
    return subTokens;
  }

  async tokenize(text, maxLength = 512) {
    if (!this.isLoaded) {
      throw new Error('Tokenizer not loaded. Call loadFromAssets() first.');
    }

    // Basic tokenization
    const basicTokens = this.basicTokenize(text);
    
    // WordPiece tokenization
    let tokens = [];
    for (const token of basicTokens) {
      const subTokens = this.wordPieceTokenize(token);
      tokens.push(...subTokens);
    }

    // Add special tokens
    tokens = ['[CLS]', ...tokens, '[SEP]'];

    // Convert to IDs
    const inputIds = tokens.map(token => 
      this.vocabToId[token] !== undefined ? this.vocabToId[token] : this.config.special_token_ids.unk_token_id
    );

    // Truncate if necessary
    if (inputIds.length > maxLength) {
      inputIds[maxLength - 1] = this.config.special_token_ids.sep_token_id; // Ensure [SEP] at end
      inputIds.splice(maxLength);
    }

    // Pad to max length
    while (inputIds.length < maxLength) {
      inputIds.push(this.config.special_token_ids.pad_token_id);
    }

    // Create attention mask
    const attentionMask = inputIds.map(id => 
      id === this.config.special_token_ids.pad_token_id ? 0 : 1
    );

    return {
      input_ids: inputIds,
      attention_mask: attentionMask,
      tokens: tokens // For debugging
    };
  }

  // Test method to verify tokenizer works
  async test() {
    const testText = "Hello world, this is a test.";
    console.log(`🧪 Testing tokenizer with: "${testText}"`);
    
    const result = await this.tokenize(testText);
    console.log('Token IDs:', result.input_ids.slice(0, 10));
    console.log('Tokens:', result.tokens.slice(0, 10));
    console.log('Attention mask:', result.attention_mask.slice(0, 10));
    
    return result;
  }
}

export default LocalTokenizer;
