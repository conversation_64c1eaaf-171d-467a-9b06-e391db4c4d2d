{"rustc": 13226066032359371072, "features": "[\"alloc\", \"dfa-onepass\", \"hybrid\", \"meta\", \"nfa-backtrack\", \"nfa-pikevm\", \"nfa-thompson\", \"perf-inline\", \"perf-literal\", \"perf-literal-multisubstring\", \"perf-literal-substring\", \"std\", \"syntax\"]", "declared_features": "[\"alloc\", \"default\", \"dfa\", \"dfa-build\", \"dfa-onepass\", \"dfa-search\", \"hybrid\", \"internal-instrument\", \"internal-instrument-pikevm\", \"logging\", \"meta\", \"nfa\", \"nfa-backtrack\", \"nfa-pikevm\", \"nfa-thompson\", \"perf\", \"perf-inline\", \"perf-literal\", \"perf-literal-multisubstring\", \"perf-literal-substring\", \"std\", \"syntax\", \"unicode\", \"unicode-age\", \"unicode-bool\", \"unicode-case\", \"unicode-gencat\", \"unicode-perl\", \"unicode-script\", \"unicode-segment\", \"unicode-word-boundary\"]", "target": 4726246767843925232, "profile": 2040997289075261528, "path": 3245708774478555303, "deps": [[2779309023524819297, "aho_corasick", false, 5600486843418274383], [3129130049864710036, "memchr", false, 9046139953019078646], [9408802513701742484, "regex_syntax", false, 11535522028295210963]], "local": [{"CheckDepInfo": {"dep_info": "aarch64-linux-android/release/.fingerprint/regex-automata-272ed310179fc13b/dep-lib-regex_automata", "checksum": false}}], "rustflags": [], "config": 10416180240749053715, "compile_kind": 6373778340919622063}