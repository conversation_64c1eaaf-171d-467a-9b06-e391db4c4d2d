use std::{
    ffi::CStr,
    os::raw::{c_char, c_int, c_float},
    path::Path,
};

use hnsw_rs::{
    hnswio::{HnswIo, ReloadOptions},
    hnsw::Hnsw,
    prelude::DistCosine,
};

const OK: c_int = 0;
const ERR_SEARCH: c_int = 1;

/// Search the HNSW index on disk (reloads from disk each time).
/// 
/// - `query_ptr`: pointer to float array of size `dim`
/// - `dim`: length of the vector
/// - `k`: top-k neighbors to retrieve
/// - `out_ptr`: pointer to array of `k` c_ints to write result IDs
/// - `dir_ptr`: path to index directory
/// - `base_ptr`: base filename (e.g. "index" for "index.hnsw.data")
#[unsafe(no_mangle)]
pub unsafe extern "C" fn hnsw_search(
    query_ptr: *const c_float,
    dim: usize,
    k: usize,
    out_ptr: *mut c_int,
    dir_ptr: *const c_char,
    base_ptr: *const c_char,
) -> c_int {
    if query_ptr.is_null() || out_ptr.is_null() || dir_ptr.is_null() || base_ptr.is_null() {
        return ERR_SEARCH;
    }

    let dir_cstr = unsafe { CStr::from_ptr(dir_ptr) };
    let base_cstr = unsafe { CStr::from_ptr(base_ptr) };

    let dir_str = match dir_cstr.to_str() {
        Ok(s) => s,
        Err(_) => return ERR_SEARCH,
    };
    let base_str = match base_cstr.to_str() {
        Ok(s) => s,
        Err(_) => return ERR_SEARCH,
    };

    let dir_path = Path::new(dir_str);

    let mut io = HnswIo::new(&dir_path, base_str);
    io.set_options(ReloadOptions::default().set_mmap(true));

    let index: Hnsw<f32, DistCosine> = match io.load_hnsw::<f32, DistCosine>() {
        Ok(idx) => idx,
        Err(_) => return ERR_SEARCH,
    };

    let query = unsafe { std::slice::from_raw_parts(query_ptr, dim).to_vec() };

    let ef = index.get_ef_construction().max(k);
    let results = index.search(&query, k, ef);

    for (i, neighbor) in results.iter().take(k).enumerate() {
        unsafe {
            *out_ptr.add(i) = neighbor.d_id as c_int;
        }
    }

    OK
}
